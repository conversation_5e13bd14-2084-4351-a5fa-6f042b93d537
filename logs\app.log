{"timestamp":"2025-08-13T19:39:06.880Z","level":"warn","message":"System Health","component":"database","status":"degraded","metrics":{"fallback":"mock_data"}}
{"timestamp":"2025-08-13T19:39:12.171Z","level":"warn","message":"System Health","component":"database","status":"degraded","metrics":{"fallback":"mock_data"}}
{"timestamp":"2025-08-13T20:08:18.484Z","level":"warn","message":"System Health","component":"database","status":"degraded","metrics":{"fallback":"mock_data"}}
{"timestamp":"2025-08-13T20:08:24.612Z","level":"warn","message":"System Health","component":"cache","status":"degraded","metrics":{"fallback":"memory_cache"}}
{"timestamp":"2025-08-13T20:08:24.614Z","level":"info","message":"System Health","component":"performance_monitoring","status":"healthy"}
{"timestamp":"2025-08-13T20:08:24.616Z","level":"info","message":"System Health","component":"real_time_streaming","status":"healthy","metrics":{"interval":3000,"batchSize":5}}
{"timestamp":"2025-08-13T20:08:24.617Z","level":"info","message":"System Health","component":"real_time_streaming","status":"healthy"}
{"timestamp":"2025-08-13T20:08:24.619Z","level":"info","message":"System Health","component":"application","status":"healthy","metrics":{"database":false,"cache":true,"performance":true,"streaming":true,"overall":true}}
{"timestamp":"2025-08-13T20:08:26.213Z","level":"warn","message":"System Health","component":"database","status":"degraded","metrics":{"fallback":"mock_data"}}
{"timestamp":"2025-08-13T20:18:56.517Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/+**********?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"+**********"}
{"timestamp":"2025-08-13T20:18:56.517Z","level":"error","message":"API Error Response","method":"GET","url":"http://localhost:3000/api/subscribers/+**********?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"+**********","status":400,"responseTime":"550ms","error":"validation_failed"}
{"timestamp":"2025-08-13T20:19:19.385Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/%2B**********?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"+**********"}
{"timestamp":"2025-08-13T20:19:19.385Z","level":"error","message":"API Error Response","method":"GET","url":"http://localhost:3000/api/subscribers/%2B**********?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"+**********","status":400,"responseTime":"422ms","error":"validation_failed"}
{"timestamp":"2025-08-13T20:20:49.574Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"**********"}
{"timestamp":"2025-08-13T20:20:49.574Z","level":"error","message":"API Error Response","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"**********","status":400,"responseTime":"444ms","error":"validation_failed"}
{"timestamp":"2025-08-13T20:22:41.858Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"**********"}
{"timestamp":"2025-08-13T20:22:41.858Z","level":"error","message":"API Error Response","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"**********","status":400,"responseTime":"675ms","error":"validation_failed"}
{"timestamp":"2025-08-13T20:23:05.519Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/1234567890?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"1234567890"}
{"timestamp":"2025-08-13T20:23:05.519Z","level":"error","message":"API Error Response","method":"GET","url":"http://localhost:3000/api/subscribers/1234567890?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"1234567890","status":400,"responseTime":"1781ms","error":"validation_failed"}
{"timestamp":"2025-08-13T20:24:03.056Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"**********"}
{"timestamp":"2025-08-13T20:24:05.261Z","level":"info","message":"Business Event","event":"SUBSCRIBER_LOOKUP","entityType":"subscriber","entityId":"**********","details":{"searchType":"msisdn","dataSource":"hybrid_system"}}
{"timestamp":"2025-08-13T20:24:03.056Z","level":"info","message":"API Response","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093","ip":"::1","operation":"get_subscriber","subscriberId":"**********","status":200,"responseTime":"2740ms"}
{"timestamp":"2025-08-13T20:26:06.173Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/1234567890?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"1234567890"}
{"timestamp":"2025-08-13T20:26:09.825Z","level":"info","message":"Business Event","event":"SUBSCRIBER_LOOKUP","entityType":"subscriber","entityId":"1234567890","details":{"searchType":"msisdn","dataSource":"hybrid_system"}}
{"timestamp":"2025-08-13T20:26:06.173Z","level":"info","message":"API Response","method":"GET","url":"http://localhost:3000/api/subscribers/1234567890?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"1234567890","status":200,"responseTime":"4818ms"}
{"timestamp":"2025-08-13T20:26:11.811Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"**********"}
{"timestamp":"2025-08-13T20:26:14.376Z","level":"info","message":"Business Event","event":"SUBSCRIBER_LOOKUP","entityType":"subscriber","entityId":"**********","details":{"searchType":"msisdn","dataSource":"hybrid_system"}}
{"timestamp":"2025-08-13T20:26:11.811Z","level":"info","message":"API Response","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"**********","status":200,"responseTime":"3952ms"}
{"timestamp":"2025-08-13T20:29:58.215Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"**********"}
{"timestamp":"2025-08-13T20:30:00.312Z","level":"info","message":"Business Event","event":"SUBSCRIBER_LOOKUP","entityType":"subscriber","entityId":"**********","details":{"searchType":"msisdn","dataSource":"hybrid_system"}}
{"timestamp":"2025-08-13T20:29:58.215Z","level":"info","message":"API Response","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"**********","status":200,"responseTime":"3360ms"}
{"timestamp":"2025-08-13T20:38:58.039Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"**********"}
{"timestamp":"2025-08-13T20:38:59.822Z","level":"info","message":"Business Event","event":"SUBSCRIBER_LOOKUP","entityType":"subscriber","entityId":"**********","details":{"searchType":"msisdn","dataSource":"hybrid_system"}}
{"timestamp":"2025-08-13T20:38:58.039Z","level":"info","message":"API Response","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"**********","status":200,"responseTime":"2374ms"}
{"timestamp":"2025-08-13T20:39:45.535Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/1234567890?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"1234567890"}
{"timestamp":"2025-08-13T20:39:47.194Z","level":"info","message":"Business Event","event":"SUBSCRIBER_LOOKUP","entityType":"subscriber","entityId":"1234567890","details":{"searchType":"msisdn","dataSource":"hybrid_system"}}
{"timestamp":"2025-08-13T20:39:45.535Z","level":"info","message":"API Response","method":"GET","url":"http://localhost:3000/api/subscribers/1234567890?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"1234567890","status":200,"responseTime":"1846ms"}
{"timestamp":"2025-08-13T20:39:47.453Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"**********"}
{"timestamp":"2025-08-13T20:39:49.100Z","level":"info","message":"Business Event","event":"SUBSCRIBER_LOOKUP","entityType":"subscriber","entityId":"**********","details":{"searchType":"msisdn","dataSource":"hybrid_system"}}
{"timestamp":"2025-08-13T20:39:47.453Z","level":"info","message":"API Response","method":"GET","url":"http://localhost:3000/api/subscribers/**********?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"**********","status":200,"responseTime":"1833ms"}
{"timestamp":"2025-08-13T20:40:30.936Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/%2B1234567890?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"+1234567890"}
{"timestamp":"2025-08-13T20:40:32.564Z","level":"info","message":"Business Event","event":"SUBSCRIBER_LOOKUP","entityType":"subscriber","entityId":"+1234567890","details":{"searchType":"msisdn","dataSource":"hybrid_system"}}
{"timestamp":"2025-08-13T20:40:30.936Z","level":"info","message":"API Response","method":"GET","url":"http://localhost:3000/api/subscribers/%2B1234567890?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"+1234567890","status":200,"responseTime":"1876ms"}
{"timestamp":"2025-08-13T20:40:32.745Z","level":"info","message":"API Request","method":"GET","url":"http://localhost:3000/api/subscribers/%2B**********?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"+**********"}
{"timestamp":"2025-08-13T20:40:34.371Z","level":"info","message":"Business Event","event":"SUBSCRIBER_LOOKUP","entityType":"subscriber","entityId":"+**********","details":{"searchType":"msisdn","dataSource":"hybrid_system"}}
{"timestamp":"2025-08-13T20:40:32.745Z","level":"info","message":"API Response","method":"GET","url":"http://localhost:3000/api/subscribers/%2B**********?type=msisdn","userAgent":"node","ip":"::1","operation":"get_subscriber","subscriberId":"+**********","status":200,"responseTime":"1758ms"}
